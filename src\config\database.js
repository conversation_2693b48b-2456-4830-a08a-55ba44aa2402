import { neon } from '@neondatabase/serverless';

// Get database URL from environment variables
const getDatabaseUrl = () => {
  let url;

  // For Vite environment (browser)
  try {
    if (import.meta && import.meta.env) {
      url = import.meta.env.VITE_DATABASE_URL;
    }
  } catch (e) {
    // import.meta not available, continue to next check
  }

  // For Node.js environment
  if (!url) {
    try {
      if (typeof process !== 'undefined' && process.env) {
        url = process.env.DATABASE_URL || process.env.VITE_DATABASE_URL;
      }
    } catch (e) {
      // process not available, continue
    }
  }

  // If no URL found in environment variables, use hardcoded URL for browser
  if (!url) {
    console.warn('⚠️ DATABASE_URL not found in environment variables, using hardcoded URL');
    url = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
  }

  return url;
};

// Create database connection
export const sql = neon(getDatabaseUrl());

// Test database connection
export const testConnection = async () => {
  try {
    const result = await sql`SELECT 1 as test`;
    console.log('✅ Database connection successful:', result);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
};

// Initialize database schema
export const initializeDatabase = async () => {
  try {
    console.log('🔄 Initializing database schema...');

    // Test connection first
    await sql`SELECT 1 as test`;

    // Create vendors table
    await sql`
      CREATE TABLE IF NOT EXISTS vendors (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        gst_number VARCHAR(50),
        description TEXT,
        services JSONB DEFAULT '[]',
        website VARCHAR(255),
        type_of_work VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Pending',
        files JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_orders INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create clients table
    await sql`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        dpiit_registered BOOLEAN DEFAULT FALSE,
        dpiit_number VARCHAR(100),
        files JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create orders table
    await sql`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        reference_number VARCHAR(100) UNIQUE NOT NULL,
        client_id INTEGER REFERENCES clients(id),
        vendor_id INTEGER REFERENCES vendors(id),
        description TEXT,
        amount DECIMAL(15,2),
        status VARCHAR(50) DEFAULT 'Pending',
        priority VARCHAR(50) DEFAULT 'Medium',
        files JSONB DEFAULT '{}',
        status_history JSONB DEFAULT '[]',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create sub_admins table
    await sql`
      CREATE TABLE IF NOT EXISTS sub_admins (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        onboarding_date DATE,
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255) UNIQUE,
        pan_number VARCHAR(20),
        term_of_work VARCHAR(100),
        files JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create type_of_work table
    await sql`
      CREATE TABLE IF NOT EXISTS type_of_work (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Database schema initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Database schema initialization failed:', error);
    return false;
  }
};

// Insert sample data
export const insertSampleData = async () => {
  try {
    console.log('🔄 Inserting sample data...');

    // Check if data already exists
    const existingVendors = await sql`SELECT COUNT(*) as count FROM vendors`;
    if (existingVendors[0].count > 0) {
      console.log('📊 Sample data already exists, skipping insertion');
      return true;
    }

    // Insert sample vendors (using exact column names from database)
    await sql`
      INSERT INTO vendors (id, name, company, "companyName", "companyType", "onboardingDate", email, phone, address, country, state, city, username, "gstNumber", specialization, "typeOfWork", rating, "isActive", "updatedAt")
      VALUES
      ('vendor-1', 'TechCorp Admin', 'TechCorp Solutions', 'TechCorp Solutions', 'Pvt. Company', '2024-01-15', '<EMAIL>', '******-123-4567', '123 Tech Street, Silicon Valley', 'United States', 'California', 'San Francisco', 'techcorp_admin', 'GST123456789', 'Leading technology solutions provider', '{"Software Development", "IT Consulting", "Cloud Services"}', 4.8, true, NOW()),
      ('vendor-2', 'Global Supplies Admin', 'Global Supplies Inc', 'Global Supplies Inc', 'MSME', '2024-02-10', '<EMAIL>', '******-987-6543', '456 Supply Ave, Chicago', 'United States', 'Illinois', 'Chicago', 'global_supplies', 'GST987654321', 'Reliable office supplies vendor', '{"Office Supplies", "Furniture", "Equipment"}', 4.5, true, NOW())
    `;

    // Insert sample clients
    await sql`
      INSERT INTO clients (company_name, company_type, onboarding_date, emails, phones, address, country, state, city, dpiit_registered, dpiit_number, files, status)
      VALUES
      ('Innovate Startup', 'Startup', '2024-03-01', '["<EMAIL>", "<EMAIL>"]', '["******-111-2222", "******-111-2223"]', '789 Innovation Blvd, Austin', 'United States', 'Texas', 'Austin', TRUE, 'DPIIT2024001', '{"dpiitCertificate": {"name": "DPIIT_Certificate.pdf", "size": "256 KB"}, "companyDocuments": [{"name": "Incorporation_Certificate.pdf", "size": "189 KB"}]}', 'Active'),
      ('Enterprise Corp', 'Large Entity', '2024-01-20', '["<EMAIL>"]', '["******-333-4444"]', '321 Business Park, New York', 'United States', 'New York', 'New York', FALSE, NULL, '{"companyDocuments": [{"name": "Business_License.pdf", "size": "234 KB"}]}', 'Active')
    `;

    // Insert sample sub-admins
    await sql`
      INSERT INTO sub_admins (name, email, onboarding_date, address, country, state, city, username, pan_number, term_of_work, files, status)
      VALUES
      ('John Smith', '<EMAIL>', '2024-01-10', '123 Admin Street, Boston', 'United States', 'Massachusetts', 'Boston', 'john_admin', '**********', 'Full-time', '{"tdsFile": {"name": "TDS_Certificate.pdf", "size": "145 KB"}, "ndaFile": {"name": "NDA_Signed.pdf", "size": "123 KB"}, "employmentFile": {"name": "Employment_Agreement.pdf", "size": "234 KB"}, "panFile": {"name": "PAN_Card.pdf", "size": "89 KB"}}', 'Active'),
      ('Sarah Johnson', '<EMAIL>', '2024-02-15', '456 Manager Ave, Seattle', 'United States', 'Washington', 'Seattle', 'sarah_admin', '**********', 'Part-time', '{"tdsFile": {"name": "TDS_Document.pdf", "size": "156 KB"}, "panFile": {"name": "PAN_Copy.pdf", "size": "92 KB"}}', 'Active')
    `;

    // Insert sample type of work
    await sql`
      INSERT INTO type_of_work (name, description, status)
      VALUES
      ('Software Development', 'Custom software development and programming services', 'Active'),
      ('IT Consulting', 'Information technology consulting and advisory services', 'Active'),
      ('Office Supplies', 'Office equipment and supplies procurement', 'Active'),
      ('Marketing Services', 'Digital marketing and advertising services', 'Active'),
      ('Legal Services', 'Legal consultation and documentation services', 'Active')
    `;

    // Insert sample orders (using correct column names from database schema)
    await sql`
      INSERT INTO orders (reference_number, client_id, vendor_id, description, amount, status, priority, files, status_history)
      VALUES
      ('IS-2024-001', 1, 1, 'Custom CRM Development - Custom CRM system development', 150000.00, 'IN_PROGRESS', 'HIGH', '{}', '[{"status": "IN_PROGRESS", "date": "2024-01-15", "description": "Order created and work started"}]'),
      ('IS-2024-002', 2, 2, 'Office Equipment - Office furniture and equipment procurement', 75000.00, 'COMPLETED', 'MEDIUM', '{}', '[{"status": "COMPLETED", "date": "2024-02-28", "description": "Order completed successfully"}]'),
      ('IS-2024-003', 1, 3, 'Patent Filing - Patent application and documentation', 245000.00, 'PENDING', 'HIGH', '{}', '[{"status": "PENDING", "date": "2024-06-20", "description": "Order received, pending review"}]')
    `;

    console.log('✅ Sample data inserted successfully');
    return true;
  } catch (error) {
    console.error('❌ Sample data insertion failed:', error);
    return false;
  }
};
