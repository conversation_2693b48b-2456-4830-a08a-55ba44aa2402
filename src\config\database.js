import { neon } from '@neondatabase/serverless';

// Get database URL from environment variables
const getDatabaseUrl = () => {
  let url;

  // For Vite environment (browser)
  try {
    if (import.meta && import.meta.env) {
      url = import.meta.env.VITE_DATABASE_URL;
    }
  } catch (e) {
    // import.meta not available, continue to next check
  }

  // For Node.js environment
  if (!url) {
    try {
      if (typeof process !== 'undefined' && process.env) {
        url = process.env.DATABASE_URL || process.env.VITE_DATABASE_URL;
      }
    } catch (e) {
      // process not available, continue
    }
  }

  // If no URL found in environment variables, use hardcoded URL for browser
  if (!url) {
    console.warn('⚠️ DATABASE_URL not found in environment variables, using hardcoded URL');
    url = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
  }

  return url;
};

// Create database connection
export const sql = neon(getDatabaseUrl());

// Test database connection
export const testConnection = async () => {
  try {
    const result = await sql`SELECT 1 as test`;
    console.log('✅ Database connection successful:', result);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
};

// Initialize database schema
export const initializeDatabase = async () => {
  try {
    console.log('🔄 Initializing database schema...');

    // Test connection first
    await sql`SELECT 1 as test`;

    // Create vendors table
    await sql`
      CREATE TABLE IF NOT EXISTS vendors (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255),
        gst_number VARCHAR(50),
        description TEXT,
        services JSONB DEFAULT '[]',
        website VARCHAR(255),
        type_of_work VARCHAR(255),
        status VARCHAR(50) DEFAULT 'Pending',
        files JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_orders INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create clients table
    await sql`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        company_name VARCHAR(255) NOT NULL,
        company_type VARCHAR(100),
        onboarding_date DATE,
        emails JSONB DEFAULT '[]',
        phones JSONB DEFAULT '[]',
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        dpiit_registered BOOLEAN DEFAULT FALSE,
        dpiit_number VARCHAR(100),
        files JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create orders table
    await sql`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        reference_number VARCHAR(100) UNIQUE NOT NULL,
        client_id INTEGER REFERENCES clients(id),
        vendor_id INTEGER REFERENCES vendors(id),
        description TEXT,
        amount DECIMAL(15,2),
        status VARCHAR(50) DEFAULT 'Pending',
        priority VARCHAR(50) DEFAULT 'Medium',
        files JSONB DEFAULT '{}',
        status_history JSONB DEFAULT '[]',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create sub_admins table
    await sql`
      CREATE TABLE IF NOT EXISTS sub_admins (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        onboarding_date DATE,
        address TEXT,
        country VARCHAR(100),
        state VARCHAR(100),
        city VARCHAR(100),
        username VARCHAR(255) UNIQUE,
        pan_number VARCHAR(20),
        term_of_work VARCHAR(100),
        files JSONB DEFAULT '{}',
        status VARCHAR(50) DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create type_of_work table
    await sql`
      CREATE TABLE IF NOT EXISTS type_of_work (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        "isActive" BOOLEAN DEFAULT TRUE,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Database schema initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Database schema initialization failed:', error);
    return false;
  }
};

// Insert sample data
export const insertSampleData = async () => {
  try {
    console.log('🔄 Inserting sample data...');

    // Check if data already exists
    const existingVendors = await sql`SELECT COUNT(*) as count FROM vendors`;
    if (existingVendors[0].count > 0) {
      console.log('📊 Sample data already exists, skipping insertion');
      return true;
    }

    // Create admin user for foreign key constraints
    try {
      await sql`
        INSERT INTO users (id, email, name, password, "createdAt", "updatedAt")
        VALUES ('admin', '<EMAIL>', 'Admin User', 'admin123', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (id) DO NOTHING
      `;
    } catch (userError) {
      console.log('⚠️ User creation failed, continuing without foreign key:', userError.message);
    }

    // Insert sample vendors (using actual database schema)
    await sql`
      INSERT INTO vendors (id, name, email, phone, company, country, address, specialization, "onboardingDate", "companyType", "companyName", city, state, username, "gstNumber", "typeOfWork", "isActive", "createdAt", "updatedAt", "createdById", rating)
      VALUES
      ('vendor-1', 'TechCorp Solutions', '<EMAIL>', '+91-**********', 'TechCorp Solutions Pvt Ltd', 'India', '123 Tech Street, Bangalore', 'Leading technology solutions provider', '2024-01-15', 'Pvt. Company', 'TechCorp Solutions', 'Bangalore', 'Karnataka', 'techcorp_admin', 'GST29**********1Z5', ARRAY['Software Development', 'IT Consulting'], true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', 4.8),
      ('vendor-2', 'Global Supplies Inc', '<EMAIL>', '+91-**********', 'Global Supplies Inc', 'India', '456 Supply Avenue, Mumbai', 'Reliable office supplies vendor', '2024-02-10', 'MSME', 'Global Supplies Inc', 'Mumbai', 'Maharashtra', 'global_supplies', 'GST27**********2L6', ARRAY['Office Supplies', 'Furniture'], true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', 4.5),
      ('vendor-3', 'Legal Associates', '<EMAIL>', '+91-**********', 'Legal Associates LLP', 'India', '789 Law Street, Delhi', 'Professional legal services', '2024-01-20', 'Partnership', 'Legal Associates', 'New Delhi', 'Delhi', 'legal_admin', 'GST07KLMNO9012P3Q7', ARRAY['Legal Services', 'Patent Filing'], true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', 4.7)
    `;

    // Insert sample clients
    await sql`
      INSERT INTO clients (company_name, company_type, onboarding_date, emails, phones, address, country, state, city, dpiit_registered, dpiit_number, files, status)
      VALUES
      ('Innovate Startup', 'Startup', '2024-03-01', '["<EMAIL>", "<EMAIL>"]', '["******-111-2222", "******-111-2223"]', '789 Innovation Blvd, Austin', 'United States', 'Texas', 'Austin', TRUE, 'DPIIT2024001', '{"dpiitCertificate": {"name": "DPIIT_Certificate.pdf", "size": "256 KB"}, "companyDocuments": [{"name": "Incorporation_Certificate.pdf", "size": "189 KB"}]}', 'Active'),
      ('Enterprise Corp', 'Large Entity', '2024-01-20', '["<EMAIL>"]', '["******-333-4444"]', '321 Business Park, New York', 'United States', 'New York', 'New York', FALSE, NULL, '{"companyDocuments": [{"name": "Business_License.pdf", "size": "234 KB"}]}', 'Active')
    `;

    // Insert sample sub-admins
    await sql`
      INSERT INTO sub_admins (name, email, onboarding_date, address, country, state, city, username, pan_number, term_of_work, files, status)
      VALUES
      ('John Smith', '<EMAIL>', '2024-01-10', '123 Admin Street, Boston', 'United States', 'Massachusetts', 'Boston', 'john_admin', '**********', 'Full-time', '{"tdsFile": {"name": "TDS_Certificate.pdf", "size": "145 KB"}, "ndaFile": {"name": "NDA_Signed.pdf", "size": "123 KB"}, "employmentFile": {"name": "Employment_Agreement.pdf", "size": "234 KB"}, "panFile": {"name": "PAN_Card.pdf", "size": "89 KB"}}', 'Active'),
      ('Sarah Johnson', '<EMAIL>', '2024-02-15', '456 Manager Ave, Seattle', 'United States', 'Washington', 'Seattle', 'sarah_admin', '**********', 'Part-time', '{"tdsFile": {"name": "TDS_Document.pdf", "size": "156 KB"}, "panFile": {"name": "PAN_Copy.pdf", "size": "92 KB"}}', 'Active')
    `;

    // Insert sample type of work
    await sql`
      INSERT INTO type_of_work (id, name, description, "isActive", "createdAt", "updatedAt", "createdById")
      VALUES
      ('work-1', 'Software Development', 'Custom software development and programming services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-2', 'IT Consulting', 'Information technology consulting and advisory services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-3', 'Office Supplies', 'Office equipment and supplies procurement', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-4', 'Marketing Services', 'Digital marketing and advertising services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-5', 'Legal Services', 'Legal consultation and documentation services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-6', 'Patent Filing', 'Patent application and documentation services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-7', 'Custom CRM Development', 'Custom CRM system development and implementation', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin')
    `;

    // Insert sample orders (using correct column names from database schema)
    await sql`
      INSERT INTO orders (reference_number, client_id, vendor_id, description, amount, status, priority, files, status_history)
      VALUES
      ('IS-2024-001', 1, 1, 'Custom CRM Development - Custom CRM system development', 150000.00, 'IN_PROGRESS', 'HIGH', '{}', '[{"status": "IN_PROGRESS", "date": "2024-01-15", "description": "Order created and work started"}]'),
      ('IS-2024-002', 2, 2, 'Office Equipment - Office furniture and equipment procurement', 75000.00, 'COMPLETED', 'MEDIUM', '{}', '[{"status": "COMPLETED", "date": "2024-02-28", "description": "Order completed successfully"}]'),
      ('IS-2024-003', 1, 3, 'Patent Filing - Patent application and documentation', 245000.00, 'PENDING', 'HIGH', '{}', '[{"status": "PENDING", "date": "2024-06-20", "description": "Order received, pending review"}]')
    `;

    console.log('✅ Sample data inserted successfully');
    return true;
  } catch (error) {
    console.error('❌ Sample data insertion failed:', error);
    return false;
  }
};

// Function to refresh database with corrected data
export const refreshDatabaseData = async () => {
  try {
    console.log('🔄 Refreshing database data...');

    // Clear existing data (with error handling)
    try {
      await sql`DELETE FROM orders`;
      console.log('✅ Cleared orders table');
    } catch (e) {
      console.log('⚠️ Could not clear orders table:', e.message);
    }

    try {
      await sql`DELETE FROM vendors`;
      console.log('✅ Cleared vendors table');
    } catch (e) {
      console.log('⚠️ Could not clear vendors table:', e.message);
    }

    try {
      await sql`DELETE FROM clients`;
      console.log('✅ Cleared clients table');
    } catch (e) {
      console.log('⚠️ Could not clear clients table:', e.message);
    }

    try {
      await sql`DELETE FROM type_of_work`;
      console.log('✅ Cleared type_of_work table');
    } catch (e) {
      console.log('⚠️ Could not clear type_of_work table:', e.message);
    }

    try {
      await sql`DELETE FROM sub_admins`;
      console.log('✅ Cleared sub_admins table');
    } catch (e) {
      console.log('⚠️ Could not clear sub_admins table:', e.message);
    }

    // Reset sequences (with error handling)
    try {
      await sql`ALTER SEQUENCE vendors_id_seq RESTART WITH 1`;
    } catch (e) {
      console.log('⚠️ vendors_id_seq not found, skipping reset');
    }

    try {
      await sql`ALTER SEQUENCE clients_id_seq RESTART WITH 1`;
    } catch (e) {
      console.log('⚠️ clients_id_seq not found, skipping reset');
    }

    try {
      await sql`ALTER SEQUENCE orders_id_seq RESTART WITH 1`;
    } catch (e) {
      console.log('⚠️ orders_id_seq not found, skipping reset');
    }

    try {
      await sql`ALTER SEQUENCE type_of_work_id_seq RESTART WITH 1`;
    } catch (e) {
      console.log('⚠️ type_of_work_id_seq not found, skipping reset');
    }

    try {
      await sql`ALTER SEQUENCE sub_admins_id_seq RESTART WITH 1`;
    } catch (e) {
      console.log('⚠️ sub_admins_id_seq not found, skipping reset');
    }

    // Re-insert sample data with correct schema

    // Create admin user for foreign key constraints
    try {
      await sql`
        INSERT INTO users (id, email, name, password, "createdAt", "updatedAt")
        VALUES ('admin', '<EMAIL>', 'Admin User', 'admin123', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (id) DO NOTHING
      `;
    } catch (userError) {
      console.log('⚠️ User creation failed, continuing without foreign key:', userError.message);
    }

    // Insert sample vendors (using actual database schema)
    await sql`
      INSERT INTO vendors (id, name, email, phone, company, country, address, specialization, "onboardingDate", "companyType", "companyName", city, state, username, "gstNumber", "typeOfWork", "isActive", "createdAt", "updatedAt", "createdById", rating)
      VALUES
      ('vendor-1', 'TechCorp Solutions', '<EMAIL>', '+91-**********', 'TechCorp Solutions Pvt Ltd', 'India', '123 Tech Street, Bangalore', 'Leading technology solutions provider', '2024-01-15', 'Pvt. Company', 'TechCorp Solutions', 'Bangalore', 'Karnataka', 'techcorp_admin', 'GST29**********1Z5', ARRAY['Software Development', 'IT Consulting'], true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', 4.8),
      ('vendor-2', 'Global Supplies Inc', '<EMAIL>', '+91-**********', 'Global Supplies Inc', 'India', '456 Supply Avenue, Mumbai', 'Reliable office supplies vendor', '2024-02-10', 'MSME', 'Global Supplies Inc', 'Mumbai', 'Maharashtra', 'global_supplies', 'GST27**********2L6', ARRAY['Office Supplies', 'Furniture'], true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', 4.5),
      ('vendor-3', 'Legal Associates', '<EMAIL>', '+91-**********', 'Legal Associates LLP', 'India', '789 Law Street, Delhi', 'Professional legal services', '2024-01-20', 'Partnership', 'Legal Associates', 'New Delhi', 'Delhi', 'legal_admin', 'GST07KLMNO9012P3Q7', ARRAY['Legal Services', 'Patent Filing'], true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', 4.7)
    `;

    // Insert sample clients
    await sql`
      INSERT INTO clients (company_name, company_type, onboarding_date, emails, phones, address, country, state, city, dpiit_registered, dpiit_number, files, status)
      VALUES
      ('Innovate Startup', 'Startup', '2024-03-01', '["<EMAIL>", "<EMAIL>"]', '["******-111-2222", "******-111-2223"]', '789 Innovation Blvd, Austin', 'United States', 'Texas', 'Austin', TRUE, 'DPIIT2024001', '{"dpiitCertificate": {"name": "DPIIT_Certificate.pdf", "size": "256 KB"}, "companyDocuments": [{"name": "Incorporation_Certificate.pdf", "size": "189 KB"}]}', 'Active'),
      ('Enterprise Corp', 'Large Entity', '2024-01-20', '["<EMAIL>"]', '["******-333-4444"]', '321 Business Park, New York', 'United States', 'New York', 'New York', FALSE, NULL, '{"companyDocuments": [{"name": "Business_License.pdf", "size": "234 KB"}]}', 'Active'),
      ('Acme Corporation', 'Large Entity', '2024-06-20', '["<EMAIL>"]', '["******-999-0000"]', '456 Corporate Blvd, Los Angeles', 'United States', 'California', 'Los Angeles', FALSE, NULL, '{}', 'Active')
    `;

    // Insert sample type of work
    await sql`
      INSERT INTO type_of_work (id, name, description, "isActive", "createdAt", "updatedAt", "createdById")
      VALUES
      ('work-1', 'Software Development', 'Custom software development and programming services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-2', 'IT Consulting', 'Information technology consulting and advisory services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-3', 'Office Supplies', 'Office equipment and supplies procurement', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-4', 'Marketing Services', 'Digital marketing and advertising services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-5', 'Legal Services', 'Legal consultation and documentation services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-6', 'Patent Filing', 'Patent application and documentation services', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin'),
      ('work-7', 'Custom CRM Development', 'Custom CRM system development and implementation', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin')
    `;

    // Insert sample orders
    await sql`
      INSERT INTO orders (reference_number, client_id, vendor_id, description, amount, status, priority, files, status_history)
      VALUES
      ('IS-2024-001', 1, 1, 'Custom CRM Development - Custom CRM system development', 150000.00, 'IN_PROGRESS', 'HIGH', '{}', '[{"status": "IN_PROGRESS", "date": "2024-01-15", "description": "Order created and work started"}]'),
      ('IS-2024-002', 2, 2, 'Office Equipment - Office furniture and equipment procurement', 75000.00, 'COMPLETED', 'MEDIUM', '{}', '[{"status": "COMPLETED", "date": "2024-02-28", "description": "Order completed successfully"}]'),
      ('IS-2024-003', 3, 3, 'Patent Filing - Patent application and documentation', 245000.00, 'PENDING', 'HIGH', '{}', '[{"status": "PENDING", "date": "2024-06-20", "description": "Order received, pending review"}]'),
      ('IS-2024-004', 1, 2, 'IT Consulting - Technology consultation services', 85000.00, 'IN_PROGRESS', 'MEDIUM', '{}', '[{"status": "IN_PROGRESS", "date": "2024-06-25", "description": "Consultation started"}]'),
      ('IS-2024-005', 2, 1, 'Software Development - Web application development', 120000.00, 'YET_TO_START', 'HIGH', '{}', '[{"status": "YET_TO_START", "date": "2024-06-26", "description": "Order placed, waiting to start"}]')
    `;

    // Insert sample sub-admins
    await sql`
      INSERT INTO sub_admins (name, email, onboarding_date, address, country, state, city, username, pan_number, term_of_work, files, status)
      VALUES
      ('John Smith', '<EMAIL>', '2024-01-10', '123 Admin Street, Boston', 'United States', 'Massachusetts', 'Boston', 'john_admin', '**********', 'Full-time', '{"tdsFile": {"name": "TDS_Certificate.pdf", "size": "145 KB"}, "ndaFile": {"name": "NDA_Signed.pdf", "size": "123 KB"}, "employmentFile": {"name": "Employment_Agreement.pdf", "size": "234 KB"}, "panFile": {"name": "PAN_Card.pdf", "size": "89 KB"}}', 'Active'),
      ('Sarah Johnson', '<EMAIL>', '2024-02-15', '456 Manager Ave, Seattle', 'United States', 'Washington', 'Seattle', 'sarah_admin', '**********', 'Part-time', '{"tdsFile": {"name": "TDS_Document.pdf", "size": "156 KB"}, "panFile": {"name": "PAN_Copy.pdf", "size": "92 KB"}}', 'Active')
    `;

    console.log('✅ Database data refreshed successfully');
    return true;
  } catch (error) {
    console.error('❌ Error refreshing database data:', error);
    throw error;
  }
};

// Simple function to ensure sample orders exist
export const ensureSampleOrders = async () => {
  try {
    console.log('🔄 Ensuring sample orders exist...');

    // Check if orders exist
    const existingOrders = await sql`SELECT COUNT(*) as count FROM orders`;
    const orderCount = parseInt(existingOrders[0].count);

    if (orderCount > 0) {
      console.log(`✅ Found ${orderCount} existing orders`);
      return true;
    }

    console.log('📝 No orders found, creating sample orders...');

    // Create sample orders with minimal data (client_id and vendor_id can be null)
    await sql`
      INSERT INTO orders (id, reference_number, description, amount, status, priority, files, status_history)
      VALUES
      (1, 'IS-2024-001', 'Patent Filing - Patent application and documentation', 245000.00, 'Completed', 'HIGH', '{}', '[{"status": "Completed", "date": "2024-07-18", "description": "Work completed successfully"}]'),
      (2, 'IS-2024-002', 'Office Equipment - Office furniture and equipment procurement', 189000.00, 'Pending with client', 'MEDIUM', '{}', '[{"status": "Pending with client", "date": "2024-06-25", "description": "Waiting for client documents"}]'),
      (3, 'IS-2024-003', 'Copyright Registration - Copyright application processing', 75000.00, 'Pending with Vendor', 'MEDIUM', '{}', '[{"status": "Pending with Vendor", "date": "2024-06-26", "description": "Vendor working on documentation"}]')
    `;

    console.log('✅ Sample orders created successfully');
    return true;
  } catch (error) {
    console.error('❌ Error ensuring sample orders:', error);
    console.error('❌ Error details:', error.message);

    // If that fails, try with even more minimal data
    try {
      console.log('🔄 Trying with minimal order data...');
      await sql`INSERT INTO orders DEFAULT VALUES`;
      await sql`INSERT INTO orders DEFAULT VALUES`;
      await sql`INSERT INTO orders DEFAULT VALUES`;
      console.log('✅ Minimal sample orders created successfully');
      return true;
    } catch (minimalError) {
      console.error('❌ Even minimal order creation failed:', minimalError.message);

      // Last resort: try to create the table and insert
      try {
        console.log('🔄 Last resort: creating orders table and data...');
        await sql`
          CREATE TABLE IF NOT EXISTS orders (
            id SERIAL PRIMARY KEY,
            description TEXT DEFAULT 'Sample Order',
            status VARCHAR(50) DEFAULT 'Pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `;
        await sql`INSERT INTO orders (id, description, status) VALUES (1, 'Patent Filing', 'Completed')`;
        await sql`INSERT INTO orders (id, description, status) VALUES (2, 'Office Equipment', 'Pending')`;
        await sql`INSERT INTO orders (id, description, status) VALUES (3, 'Copyright Registration', 'Pending')`;
        console.log('✅ Created orders table and sample data');
        return true;
      } catch (lastError) {
        console.error('❌ Last resort failed:', lastError.message);
        return false;
      }
    }
  }
};
