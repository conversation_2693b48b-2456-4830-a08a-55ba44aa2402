import { sql } from './src/config/database.js';

async function checkDatabase() {
  console.log('🔄 Checking database contents...');

  try {
    // Check orders
    const orders = await sql`SELECT COUNT(*) as count FROM orders`;
    console.log('📊 Orders count:', orders[0]?.count || 0);

    // Check clients
    const clients = await sql`SELECT COUNT(*) as count FROM clients`;
    console.log('📊 Clients count:', clients[0]?.count || 0);

    // Check vendors
    const vendors = await sql`SELECT COUNT(*) as count FROM vendors`;
    console.log('📊 Vendors count:', vendors[0]?.count || 0);

    // If all tables are empty, populate with sample data
    if (parseInt(orders[0]?.count || 0) === 0 &&
        parseInt(clients[0]?.count || 0) === 0 &&
        parseInt(vendors[0]?.count || 0) === 0) {
      console.log('📝 Database is empty, creating sample data...');
      await createSampleData();
    }

  } catch (error) {
    console.error('❌ Error checking database:', error.message);
  }
}

async function createSampleData() {
  try {
    console.log('🔄 Creating sample data...');

    // Try to create clients with different column names
    console.log('📝 Creating clients...');
    try {
      await sql`INSERT INTO clients DEFAULT VALUES`;
      await sql`INSERT INTO clients DEFAULT VALUES`;
      await sql`INSERT INTO clients DEFAULT VALUES`;
      console.log('✅ Clients created with default values');
    } catch (clientError) {
      console.log('⚠️ Client creation failed:', clientError.message);
    }

    // Try to create vendors with different approaches
    console.log('📝 Creating vendors...');
    try {
      await sql`INSERT INTO vendors DEFAULT VALUES`;
      await sql`INSERT INTO vendors DEFAULT VALUES`;
      await sql`INSERT INTO vendors DEFAULT VALUES`;
      console.log('✅ Vendors created with default values');
    } catch (vendorError) {
      console.log('⚠️ Vendor creation failed:', vendorError.message);
    }

    // Try to create orders with minimal fields
    console.log('📝 Creating orders...');
    try {
      await sql`INSERT INTO orders DEFAULT VALUES`;
      await sql`INSERT INTO orders DEFAULT VALUES`;
      await sql`INSERT INTO orders DEFAULT VALUES`;
      console.log('✅ Orders created with default values');
    } catch (orderError) {
      console.log('⚠️ Order creation failed:', orderError.message);

      // Try with just description
      try {
        await sql`INSERT INTO orders (description) VALUES ('Sample Order 1')`;
        await sql`INSERT INTO orders (description) VALUES ('Sample Order 2')`;
        await sql`INSERT INTO orders (description) VALUES ('Sample Order 3')`;
        console.log('✅ Orders created with description only');
      } catch (descError) {
        console.log('⚠️ Even description-only orders failed:', descError.message);
      }
    }

    console.log('✅ Sample data creation completed!');

    // Verify the data was created
    const newOrders = await sql`SELECT COUNT(*) as count FROM orders`;
    const newClients = await sql`SELECT COUNT(*) as count FROM clients`;
    const newVendors = await sql`SELECT COUNT(*) as count FROM vendors`;

    console.log('📊 Final counts:');
    console.log('   Orders:', newOrders[0]?.count || 0);
    console.log('   Clients:', newClients[0]?.count || 0);
    console.log('   Vendors:', newVendors[0]?.count || 0);

  } catch (error) {
    console.error('❌ Error creating sample data:', error.message);
  }
}

checkDatabase().then(() => process.exit(0)).catch(err => {
  console.error('❌ Error:', err);
  process.exit(1);
});
