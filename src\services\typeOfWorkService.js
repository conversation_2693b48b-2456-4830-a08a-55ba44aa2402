import { sql } from '../config/database.js';

// Get all type of work entries
export const getAllTypeOfWork = async () => {
  try {
    const typeOfWork = await sql`
      SELECT 
        id,
        name,
        description,
        status,
        created_at,
        updated_at
      FROM type_of_work 
      ORDER BY created_at DESC
    `;
    return typeOfWork;
  } catch (error) {
    console.error('Error fetching type of work:', error);
    throw error;
  }
};

// Get active type of work entries (for dropdowns)
export const getActiveTypeOfWork = async () => {
  try {
    const typeOfWork = await sql`
      SELECT 
        id,
        name,
        description
      FROM type_of_work 
      WHERE status = 'Active'
      ORDER BY name ASC
    `;
    return typeOfWork;
  } catch (error) {
    console.error('Error fetching active type of work:', error);
    throw error;
  }
};

// Get type of work by ID
export const getTypeOfWorkById = async (id) => {
  try {
    const typeOfWork = await sql`
      SELECT 
        id,
        name,
        description,
        status,
        created_at,
        updated_at
      FROM type_of_work 
      WHERE id = ${id}
    `;
    return typeOfWork[0] || null;
  } catch (error) {
    console.error('Error fetching type of work by ID:', error);
    throw error;
  }
};

// Create new type of work
export const createTypeOfWork = async (typeOfWorkData) => {
  try {
    const {
      name,
      description,
      status = 'Active'
    } = typeOfWorkData;

    const typeOfWork = await sql`
      INSERT INTO type_of_work (
        name,
        description,
        status
      ) VALUES (
        ${name},
        ${description},
        ${status}
      )
      RETURNING *
    `;
    return typeOfWork[0];
  } catch (error) {
    console.error('Error creating type of work:', error);
    throw error;
  }
};

// Update type of work
export const updateTypeOfWork = async (id, typeOfWorkData) => {
  try {
    const {
      name,
      description,
      status
    } = typeOfWorkData;

    const typeOfWork = await sql`
      UPDATE type_of_work SET
        name = ${name},
        description = ${description},
        status = ${status},
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING *
    `;
    return typeOfWork[0];
  } catch (error) {
    console.error('Error updating type of work:', error);
    throw error;
  }
};

// Delete type of work
export const deleteTypeOfWork = async (id) => {
  try {
    await sql`DELETE FROM type_of_work WHERE id = ${id}`;
    return true;
  } catch (error) {
    console.error('Error deleting type of work:', error);
    throw error;
  }
};

// Get type of work statistics
export const getTypeOfWorkStats = async () => {
  try {
    const stats = await sql`
      SELECT 
        COUNT(*) as total_types,
        COUNT(CASE WHEN status = 'Active' THEN 1 END) as active_types,
        COUNT(CASE WHEN status = 'Inactive' THEN 1 END) as inactive_types
      FROM type_of_work
    `;
    return stats[0];
  } catch (error) {
    console.error('Error fetching type of work stats:', error);
    throw error;
  }
};

// Search type of work
export const searchTypeOfWork = async (searchTerm) => {
  try {
    const typeOfWork = await sql`
      SELECT 
        id,
        name,
        description,
        status,
        created_at,
        updated_at
      FROM type_of_work 
      WHERE 
        name ILIKE ${`%${searchTerm}%`} OR
        description ILIKE ${`%${searchTerm}%`}
      ORDER BY created_at DESC
    `;
    return typeOfWork;
  } catch (error) {
    console.error('Error searching type of work:', error);
    throw error;
  }
};

// Check if name exists
export const checkNameExists = async (name, excludeId = null) => {
  try {
    let query = sql`SELECT id FROM type_of_work WHERE name = ${name}`;
    
    if (excludeId) {
      query = sql`SELECT id FROM type_of_work WHERE name = ${name} AND id != ${excludeId}`;
    }
    
    const result = await query;
    return result.length > 0;
  } catch (error) {
    console.error('Error checking name exists:', error);
    throw error;
  }
};
