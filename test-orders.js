import { sql } from './src/config/database.js';

async function testOrders() {
  try {
    console.log('🔄 Testing database connection...');
    const testResult = await sql`SELECT 1 as test`;
    console.log('✅ Database connection successful:', testResult);

    console.log('🔄 Checking orders table structure...');
    const tableStructure = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'orders'
      ORDER BY ordinal_position
    `;
    console.log('📊 Orders table structure:', tableStructure);

    console.log('🔄 Fetching current orders data...');
    const orders = await sql`SELECT * FROM orders LIMIT 5`;
    console.log('📋 Current orders:', orders);

    console.log('🔄 Testing orders with joins...');
    const ordersWithJoins = await sql`
      SELECT 
        o.id,
        o.reference_number,
        o.client_id,
        o.vendor_id,
        o.description,
        o.amount,
        o.status,
        o.priority,
        o.created_at,
        o.updated_at,
        c.company_name as client_name,
        v.company_name as vendor_name
      FROM orders o
      LEFT JOIN clients c ON o.client_id = c.id
      LEFT JOIN vendors v ON o.vendor_id = v.id
      ORDER BY o.created_at DESC
      LIMIT 3
    `;
    console.log('📋 Orders with joins:', ordersWithJoins);

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testOrders();
