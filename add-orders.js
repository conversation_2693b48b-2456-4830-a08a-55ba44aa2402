import { sql } from './src/config/database.js';

async function addOrders() {
  console.log('🔄 Adding orders to database...');
  
  try {
    // Try different approaches to create orders
    console.log('📝 Attempting to create orders...');
    
    // Method 1: Try with ID, referenceNumber and description
    try {
      await sql`INSERT INTO orders (id, "referenceNumber", description) VALUES (1, 'IS-2024-001', 'Patent Filing Application')`;
      console.log('✅ Order 1 created with ID, referenceNumber and description');
    } catch (e) {
      console.log('⚠️ Method 1 failed:', e.message);
    }

    // Method 2: Try with ID, description and different status values
    try {
      await sql`INSERT INTO orders (id, description, status) VALUES (2, 'Office Equipment Purchase', 'PENDING')`;
      console.log('✅ Order 2 created with PENDING status');
    } catch (e) {
      console.log('⚠️ PENDING status failed:', e.message);

      // Try other status values
      try {
        await sql`INSERT INTO orders (id, description, status) VALUES (2, 'Office Equipment Purchase', 'ACTIVE')`;
        console.log('✅ Order 2 created with ACTIVE status');
      } catch (e2) {
        console.log('⚠️ ACTIVE status failed:', e2.message);

        try {
          await sql`INSERT INTO orders (id, description) VALUES (2, 'Office Equipment Purchase')`;
          console.log('✅ Order 2 created without status');
        } catch (e3) {
          console.log('⚠️ Order 2 completely failed:', e3.message);
        }
      }
    }

    // Method 3: Try with ID, description and amount
    try {
      await sql`INSERT INTO orders (id, description, amount) VALUES (3, 'Copyright Registration', 75000)`;
      console.log('✅ Order 3 created with ID, description and amount');
    } catch (e) {
      console.log('⚠️ Method 3 failed:', e.message);
    }

    // Method 4: Try with just ID
    try {
      await sql`INSERT INTO orders (id) VALUES (4)`;
      console.log('✅ Order 4 created with just ID');
    } catch (e) {
      console.log('⚠️ Method 4 failed:', e.message);
    }

    // Method 5: Try with ID and minimal data
    try {
      await sql`INSERT INTO orders (id, description) VALUES (5, 'Legal Consultation')`;
      console.log('✅ Order 5 created with ID and description');
    } catch (e) {
      console.log('⚠️ Method 5 failed:', e.message);
    }
    
    // Check final count
    const orders = await sql`SELECT COUNT(*) as count FROM orders`;
    console.log('📊 Final orders count:', orders[0]?.count || 0);
    
    if (parseInt(orders[0]?.count || 0) > 0) {
      const sampleOrders = await sql`SELECT * FROM orders LIMIT 5`;
      console.log('📋 Sample orders:', sampleOrders);
    }
    
  } catch (error) {
    console.error('❌ Error adding orders:', error.message);
  }
}

addOrders().then(() => process.exit(0)).catch(err => {
  console.error('❌ Error:', err);
  process.exit(1);
});
