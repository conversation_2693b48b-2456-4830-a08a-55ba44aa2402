# Innoventory Admin Panel

A modern, responsive admin panel built with React.js, Tailwind CSS, and JavaScript for comprehensive inventory management.

## 🚀 Features

### 📊 Dashboard
- **Interactive Charts**: Revenue trends, user distribution, order status with Recharts
- **Real-time Statistics**: Clickable widgets showing key metrics
- **Responsive Design**: Optimized for all screen sizes
- **Modern UI**: Gradient backgrounds, animations, and hover effects

### 👥 User Management
- **Vendors**: Complete vendor management with file uploads
- **Clients**: Client relationship management
- **Sub-admins**: Role-based access control and permissions
- **Audit Logs**: Track portal usage and user activity

### 📋 Data Management
- **Advanced Tables**: Sorting, filtering, pagination (50 rows default)
- **Export Functionality**: PDF and Excel export with column customization
- **Column Toggle**: Show/hide columns as needed
- **Search & Filter**: Multi-column filtering capabilities

### 📁 File Upload System
- **Multiple File Support**: No limit on number of files
- **File Size Limit**: 5MB maximum per file
- **Supported Formats**: PDF, Images, Word, Excel, PowerPoint
- **Duplicate Handling**: Automatic timestamp appending
- **Drag & Drop**: Intuitive file upload interface

### 🎨 Modern UI/UX
- **Responsive Design**: Mobile-first approach
- **Animations**: Smooth transitions and micro-interactions
- **Professional Styling**: Modern gradients and shadows
- **Accessibility**: WCAG compliant design

## 🛠️ Tech Stack

- **Frontend**: React.js 19.1.0
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **Charts**: Recharts
- **Icons**: Heroicons
- **File Export**: jsPDF, xlsx
- **Build Tool**: Vite

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Innoventory-AdminPannel
   ```

2. **Install dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:5173
   ```

## 🎯 Key Features

✅ **Dashboard with Interactive Charts**
✅ **Advanced Data Tables with Export**
✅ **File Upload System (5MB, Multiple Formats)**
✅ **Responsive Design**
✅ **Modern Animations & Styling**
✅ **Vendor/Client/Order Management**
✅ **Audit & Analytics**
✅ **Settings & Configuration**

## 📱 Responsive Design

- **Mobile**: Optimized for phones (320px+)
- **Tablet**: Enhanced for tablets (768px+)
- **Desktop**: Full features for desktop (1024px+)

## 🚀 Live Demo

The application is running at: `http://localhost:5173`

Navigate through different sections:
- `/dashboard` - Main dashboard with charts
- `/vendors` - Vendor management with file uploads
- `/clients` - Client management
- `/orders` - Order tracking
- `/audit` - Usage analytics
- `/sub-admins` - User management
- `/settings` - Configuration

---

**Created with ❤️ for modern inventory management needs.**
