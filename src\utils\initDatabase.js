import { testConnection, initializeDatabase, clearAllData } from '../config/database.js';

// Initialize the database when the app starts
export const initApp = async () => {
  try {
    console.log('🚀 Starting application initialization...');
    
    // Test database connection
    console.log('🔄 Testing database connection...');
    const connectionSuccess = await testConnection();
    
    if (!connectionSuccess) {
      throw new Error('Database connection failed');
    }
    
    // Initialize database schema
    console.log('🔄 Initializing database schema...');
    const schemaSuccess = await initializeDatabase();
    
    if (!schemaSuccess) {
      throw new Error('Database schema initialization failed');
    }
    
    // Clear any existing demo data
    console.log('🔄 Clearing existing demo data...');
    const clearSuccess = await clearAllData();

    if (!clearSuccess) {
      console.warn('⚠️ Data clearing failed, but continuing...');
    }
    
    console.log('✅ Application initialization completed successfully!');
    console.log('📊 Database is ready for use');
    
    return true;
  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    console.error('🔧 Please check your database configuration and try again');
    return false;
  }
};

// Check database health
export const checkDatabaseHealth = async () => {
  try {
    const isHealthy = await testConnection();
    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      message: isHealthy ? 'Database connection is working' : 'Database connection failed'
    };
  } catch (error) {
    return {
      status: 'error',
      timestamp: new Date().toISOString(),
      message: error.message
    };
  }
};
