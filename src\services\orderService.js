import { sql } from '../config/database.js';

// Get all orders with client and vendor details
export const getAllOrders = async () => {
  try {
    const orders = await sql`
      SELECT
        o.id,
        o."referenceNumber",
        o."customerId",
        o."vendorId",
        o.description,
        o.amount,
        o.status,
        o.priority,
        o."createdAt",
        o."updatedAt",
        c.company_name as client_name,
        v."companyName" as vendor_name
      FROM orders o
      LEFT JOIN clients c ON o."customerId" = c.id
      LEFT JOIN vendors v ON o."vendorId" = v.id
      ORDER BY o."createdAt" DESC
    `;

    // Transform dates to strings to prevent React errors
    return orders.map(order => ({
      ...order,
      createdAt: order.createdAt ? new Date(order.createdAt).toISOString().split('T')[0] : '',
      updatedAt: order.updatedAt ? new Date(order.updatedAt).toISOString().split('T')[0] : '',
      dueDate: order.dueDate ? new Date(order.dueDate).toISOString().split('T')[0] : '',
      startDate: order.startDate ? new Date(order.startDate).toISOString().split('T')[0] : '',
      completedDate: order.completedDate ? new Date(order.completedDate).toISOString().split('T')[0] : ''
    }));
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
};

// Get order by ID with full details
export const getOrderById = async (id) => {
  try {
    const order = await sql`
      SELECT
        o.id,
        o."referenceNumber",
        o."customerId",
        o."vendorId",
        o.description,
        o.amount,
        o.status,
        o.priority,
        o."createdAt",
        o."updatedAt",
        c.company_name as client_name,
        c.emails as client_emails,
        c.phones as client_phones,
        c.address as client_address,
        v."companyName" as vendor_name,
        v.email as vendor_email,
        v.phone as vendor_phone,
        v.address as vendor_address
      FROM orders o
      LEFT JOIN clients c ON o."customerId" = c.id
      LEFT JOIN vendors v ON o."vendorId" = v.id
      WHERE o.id = ${id}
    `;

    const orderData = order[0];
    if (!orderData) return null;

    // Transform dates to strings to prevent React errors
    return {
      ...orderData,
      createdAt: orderData.createdAt ? new Date(orderData.createdAt).toISOString().split('T')[0] : '',
      updatedAt: orderData.updatedAt ? new Date(orderData.updatedAt).toISOString().split('T')[0] : '',
      dueDate: orderData.dueDate ? new Date(orderData.dueDate).toISOString().split('T')[0] : '',
      startDate: orderData.startDate ? new Date(orderData.startDate).toISOString().split('T')[0] : '',
      completedDate: orderData.completedDate ? new Date(orderData.completedDate).toISOString().split('T')[0] : ''
    };
  } catch (error) {
    console.error('Error fetching order by ID:', error);
    throw error;
  }
};

// Generate next reference number
export const generateReferenceNumber = async () => {
  try {
    const result = await sql`
      SELECT reference_number 
      FROM orders 
      WHERE reference_number LIKE 'IS-%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    if (result.length === 0) {
      return 'IS-2024-001';
    }
    
    const lastRef = result[0].reference_number;
    const lastNumber = parseInt(lastRef.split('-')[2]);
    const nextNumber = (lastNumber + 1).toString().padStart(3, '0');
    return `IS-2024-${nextNumber}`;
  } catch (error) {
    console.error('Error generating reference number:', error);
    return `IS-2024-${Date.now().toString().slice(-3)}`;
  }
};

// Create new order
export const createOrder = async (orderData) => {
  try {
    const {
      clientId,
      vendorId,
      description,
      amount,
      status = 'Pending',
      priority = 'Medium',
      files = {}
    } = orderData;

    const referenceNumber = await generateReferenceNumber();
    
    const statusHistory = [{
      status: 'Pending',
      date: new Date().toISOString().split('T')[0],
      description: 'Order created'
    }];

    const order = await sql`
      INSERT INTO orders (
        reference_number,
        client_id,
        vendor_id,
        description,
        amount,
        status,
        priority,
        files,
        status_history
      ) VALUES (
        ${referenceNumber},
        ${clientId},
        ${vendorId},
        ${description},
        ${amount},
        ${status},
        ${priority},
        ${JSON.stringify(files)},
        ${JSON.stringify(statusHistory)}
      )
      RETURNING *
    `;
    return order[0];
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

// Update order
export const updateOrder = async (id, orderData) => {
  try {
    const {
      clientId,
      vendorId,
      description,
      amount,
      status,
      priority,
      files,
      statusHistory
    } = orderData;

    const order = await sql`
      UPDATE orders SET
        client_id = ${clientId},
        vendor_id = ${vendorId},
        description = ${description},
        amount = ${amount},
        status = ${status},
        priority = ${priority},
        files = ${JSON.stringify(files)},
        status_history = ${JSON.stringify(statusHistory)},
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING *
    `;
    return order[0];
  } catch (error) {
    console.error('Error updating order:', error);
    throw error;
  }
};

// Delete order
export const deleteOrder = async (id) => {
  try {
    const result = await sql`
      DELETE FROM orders
      WHERE id = ${id}
      RETURNING id
    `;

    if (result.length === 0) {
      throw new Error('Order not found');
    }

    return result[0];
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
};

// Update order status
export const updateOrderStatus = async (id, newStatus, comment) => {
  try {
    // Get current order
    const currentOrder = await getOrderById(id);
    if (!currentOrder) {
      throw new Error('Order not found');
    }

    // Update status history
    const statusHistory = currentOrder.status_history || [];
    statusHistory.push({
      status: newStatus,
      date: new Date().toISOString().split('T')[0],
      description: comment || `Status changed to ${newStatus}`
    });

    const order = await sql`
      UPDATE orders SET
        status = ${newStatus},
        status_history = ${JSON.stringify(statusHistory)},
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING *
    `;
    return order[0];
  } catch (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
};

// Delete order
export const deleteOrder = async (id) => {
  try {
    await sql`DELETE FROM orders WHERE id = ${id}`;
    return true;
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
};

// Get order statistics
export const getOrderStats = async () => {
  try {
    const stats = await sql`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as in_progress_orders,
        COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_orders,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        COUNT(CASE WHEN priority = 'High' THEN 1 END) as high_priority_orders,
        COUNT(CASE WHEN priority = 'Medium' THEN 1 END) as medium_priority_orders,
        COUNT(CASE WHEN priority = 'Low' THEN 1 END) as low_priority_orders
      FROM orders
    `;
    return stats[0];
  } catch (error) {
    console.error('Error fetching order stats:', error);
    throw error;
  }
};

// Search orders
export const searchOrders = async (searchTerm) => {
  try {
    const orders = await sql`
      SELECT 
        o.id,
        o.reference_number,
        o.client_id,
        o.vendor_id,
        o.description,
        o.amount,
        o.status,
        o.priority,
        o.files,
        o.status_history,
        o.created_at,
        o.updated_at,
        c.company_name as client_name,
        v.company_name as vendor_name
      FROM orders o
      LEFT JOIN clients c ON o.client_id = c.id
      LEFT JOIN vendors v ON o.vendor_id = v.id
      WHERE 
        o.reference_number ILIKE ${`%${searchTerm}%`} OR
        o.description ILIKE ${`%${searchTerm}%`} OR
        c.company_name ILIKE ${`%${searchTerm}%`} OR
        v.company_name ILIKE ${`%${searchTerm}%`}
      ORDER BY o.created_at DESC
    `;
    return orders;
  } catch (error) {
    console.error('Error searching orders:', error);
    throw error;
  }
};
