import { sql } from '../config/database.js';

// Get all orders with client and vendor details
export const getAllOrders = async () => {
  try {
    console.log('🔄 Fetching orders from database...');

    // First, test database connection
    await sql`SELECT 1 as test`;
    console.log('✅ Database connection successful');

    // Check if orders table exists
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'orders'
      )
    `;

    if (!tableExists[0].exists) {
      console.log('⚠️ Orders table does not exist, returning empty array');
      return [];
    }

    // Check if orders table has data
    let orders;
    try {
      orders = await sql`
        SELECT
          o.id,
          o.reference_number,
          o.client_id,
          o.vendor_id,
          o.description,
          o.amount,
          o.status,
          o.priority,
          o.created_at,
          o.updated_at,
          c.company_name as client_name,
          v.company_name as vendor_name
        FROM orders o
        LEFT JOIN clients c ON o.client_id = c.id
        LEFT JOIN vendors v ON o.vendor_id = v.id
        ORDER BY o.created_at DESC
      `;
    } catch (dbError) {
      console.warn('⚠️ Database query failed, trying simple query:', dbError.message);
      // Fallback to simple query without joins
      orders = await sql`SELECT * FROM orders ORDER BY created_at DESC`;
    }

    console.log('✅ Orders fetched successfully:', orders.length);

    console.log('✅ Orders fetched successfully:', orders?.length || 0);
    console.log('📋 Raw orders data:', orders);

    // Handle empty result
    if (!orders || orders.length === 0) {
      console.log('📝 No orders found in database, returning empty array');
      return [];
    }

    // Transform to match UI expectations and convert dates to strings
    const transformedOrders = orders.map(order => {
      try {
        return {
          id: order.id,
          orderReferenceNumber: order.reference_number || `ORDER-${order.id}`,
          orderOnboardingDate: order.created_at ? new Date(order.created_at).toISOString().split('T')[0] : '',
          client: order.client_name || `Client-${order.client_id || 'Unknown'}`,
          typeOfWork: order.description || 'General Work',
          dateOfWorkCompletionExpected: order.updated_at ? new Date(order.updated_at).toISOString().split('T')[0] : '',
          totalInvoiceValue: order.amount || 0,
          totalValueGstGovtFees: Math.round((order.amount || 0) * 0.18), // 18% GST
          dateOfPaymentExpected: order.updated_at ? new Date(order.updated_at).toISOString().split('T')[0] : '',
          dateOfOnboardingVendor: order.created_at ? new Date(order.created_at).toISOString().split('T')[0] : '',
          vendorName: order.vendor_name || `Vendor-${order.vendor_id || 'Unknown'}`,
          currentStatus: order.status || 'Pending',
          statusComments: order.description || '',
          dateOfStatusChange: order.updated_at ? new Date(order.updated_at).toISOString().split('T')[0] : '',
          dateOfWorkCompletionExpectedFromVendor: order.updated_at ? new Date(order.updated_at).toISOString().split('T')[0] : '',
          amountToBePaidToVendor: Math.round((order.amount || 0) * 0.8), // 80% to vendor
          amountPaidToVendor: order.status === 'COMPLETED' ? Math.round((order.amount || 0) * 0.8) : 0
        };
      } catch (transformError) {
        console.error('⚠️ Error transforming order:', order.id, transformError);
        // Return a safe default object
        return {
          id: order.id || 'unknown',
          orderReferenceNumber: `ORDER-${order.id || 'unknown'}`,
          orderOnboardingDate: '',
          client: 'Unknown Client',
          typeOfWork: 'Unknown Work',
          dateOfWorkCompletionExpected: '',
          totalInvoiceValue: 0,
          totalValueGstGovtFees: 0,
          dateOfPaymentExpected: '',
          dateOfOnboardingVendor: '',
          vendorName: 'Unknown Vendor',
          currentStatus: 'Pending',
          statusComments: '',
          dateOfStatusChange: '',
          dateOfWorkCompletionExpectedFromVendor: '',
          amountToBePaidToVendor: 0,
          amountPaidToVendor: 0
        };
      }
    });

    console.log('🔄 Transformed orders:', transformedOrders?.length || 0);
    console.log('📊 Final orders data:', transformedOrders);
    return transformedOrders;
  } catch (error) {
    console.error('❌ Error fetching orders:', error);
    console.error('Error details:', error.message, error.stack);

    // Return empty array instead of throwing error to prevent 500
    console.log('🔄 Returning empty array to prevent 500 error');
    return [];
  }
};

// Get order by ID with full details
export const getOrderById = async (id) => {
  try {
    console.log('🔄 Fetching order by ID:', id);

    let order;
    try {
      order = await sql`
        SELECT
          o.id,
          o.reference_number,
          o.client_id,
          o.vendor_id,
          o.description,
          o.amount,
          o.status,
          o.priority,
          o.files,
          o.status_history,
          o.created_at,
          o.updated_at,
          c.company_name as client_name,
          c.emails as client_emails,
          c.phones as client_phones,
          c.address as client_address,
          v.company_name as vendor_name,
          v.emails as vendor_emails,
          v.phones as vendor_phones,
          v.address as vendor_address
        FROM orders o
        LEFT JOIN clients c ON o.client_id = c.id
        LEFT JOIN vendors v ON o.vendor_id = v.id
        WHERE o.id = ${id}
      `;
    } catch (dbError) {
      console.warn('⚠️ Database query with joins failed, trying simple query:', dbError.message);
      // Fallback to simple query without joins
      order = await sql`SELECT * FROM orders WHERE id = ${id}`;
    }

    const orderData = order[0];
    if (!orderData) {
      console.log('📝 Order not found for ID:', id);
      return null;
    }

    // Transform to match UI expectations
    try {
      return {
        id: orderData.id,
        orderReferenceNumber: orderData.reference_number || `ORDER-${orderData.id}`,
        orderOnboardingDate: orderData.created_at ? new Date(orderData.created_at).toISOString().split('T')[0] : '',
        client: orderData.client_name || `Client-${orderData.client_id || 'Unknown'}`,
        typeOfWork: orderData.description || 'General Work',
        dateOfWorkCompletionExpected: orderData.updated_at ? new Date(orderData.updated_at).toISOString().split('T')[0] : '',
        totalInvoiceValue: orderData.amount || 0,
        totalValueGstGovtFees: Math.round((orderData.amount || 0) * 0.18), // 18% GST
        dateOfPaymentExpected: orderData.updated_at ? new Date(orderData.updated_at).toISOString().split('T')[0] : '',
        dateOfOnboardingVendor: orderData.created_at ? new Date(orderData.created_at).toISOString().split('T')[0] : '',
        vendorName: orderData.vendor_name || `Vendor-${orderData.vendor_id || 'Unknown'}`,
        currentStatus: orderData.status || 'Pending',
        statusComments: orderData.description || '',
        dateOfStatusChange: orderData.updated_at ? new Date(orderData.updated_at).toISOString().split('T')[0] : '',
        dateOfWorkCompletionExpectedFromVendor: orderData.updated_at ? new Date(orderData.updated_at).toISOString().split('T')[0] : '',
        amountToBePaidToVendor: Math.round((orderData.amount || 0) * 0.8), // 80% to vendor
        amountPaidToVendor: orderData.status === 'COMPLETED' ? Math.round((orderData.amount || 0) * 0.8) : 0,
        files: orderData.files || {},
        statusHistory: orderData.status_history || [],
        priority: orderData.priority || 'Medium'
      };
    } catch (transformError) {
      console.error('⚠️ Error transforming order data:', transformError);
      // Return a safe default object
      return {
        id: orderData.id || id,
        orderReferenceNumber: `ORDER-${orderData.id || id}`,
        orderOnboardingDate: '',
        client: 'Unknown Client',
        typeOfWork: 'Unknown Work',
        dateOfWorkCompletionExpected: '',
        totalInvoiceValue: 0,
        totalValueGstGovtFees: 0,
        dateOfPaymentExpected: '',
        dateOfOnboardingVendor: '',
        vendorName: 'Unknown Vendor',
        currentStatus: 'Pending',
        statusComments: '',
        dateOfStatusChange: '',
        dateOfWorkCompletionExpectedFromVendor: '',
        amountToBePaidToVendor: 0,
        amountPaidToVendor: 0,
        files: {},
        statusHistory: [],
        priority: 'Medium'
      };
    }
  } catch (error) {
    console.error('❌ Error fetching order by ID:', error);
    console.error('Error details:', error.message, error.stack);

    // Return null instead of throwing error to prevent 500
    console.log('🔄 Returning null to prevent 500 error');
    return null;
  }
};

// Generate next reference number
export const generateReferenceNumber = async () => {
  try {
    const result = await sql`
      SELECT reference_number 
      FROM orders 
      WHERE reference_number LIKE 'IS-%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    if (result.length === 0) {
      return 'IS-2024-001';
    }
    
    const lastRef = result[0].reference_number;
    const lastNumber = parseInt(lastRef.split('-')[2]);
    const nextNumber = (lastNumber + 1).toString().padStart(3, '0');
    return `IS-2024-${nextNumber}`;
  } catch (error) {
    console.error('Error generating reference number:', error);
    return `IS-2024-${Date.now().toString().slice(-3)}`;
  }
};

// Create new order
export const createOrder = async (orderData) => {
  try {
    const {
      clientId,
      vendorId,
      description,
      amount,
      status = 'Pending',
      priority = 'Medium',
      files = {}
    } = orderData;

    const referenceNumber = await generateReferenceNumber();
    
    const statusHistory = [{
      status: 'Pending',
      date: new Date().toISOString().split('T')[0],
      description: 'Order created'
    }];

    const order = await sql`
      INSERT INTO orders (
        reference_number,
        client_id,
        vendor_id,
        description,
        amount,
        status,
        priority,
        files,
        status_history
      ) VALUES (
        ${referenceNumber},
        ${clientId},
        ${vendorId},
        ${description},
        ${amount},
        ${status},
        ${priority},
        ${JSON.stringify(files)},
        ${JSON.stringify(statusHistory)}
      )
      RETURNING *
    `;
    return order[0];
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

// Update order
export const updateOrder = async (id, orderData) => {
  try {
    console.log('🔄 Updating order with ID:', id);
    console.log('📋 Order data received:', orderData);

    // First, check if the order exists and get current data
    const existingOrder = await sql`SELECT * FROM orders WHERE id = ${id}`;
    if (existingOrder.length === 0) {
      console.error(`❌ Order with ID ${id} does not exist in database`);

      // Check what orders do exist
      const allOrders = await sql`SELECT id, reference_number FROM orders ORDER BY id`;
      console.log('📋 Available orders:', allOrders);

      // If no orders exist, create sample data
      if (allOrders.length === 0) {
        console.log('🔄 No orders found, creating sample data...');
        try {
          const { ensureSampleOrders } = await import('../config/database.js');
          await ensureSampleOrders();
          console.log('✅ Sample data created, retrying...');

          // Retry getting the order
          const retryOrder = await sql`SELECT * FROM orders WHERE id = ${id}`;
          if (retryOrder.length === 0) {
            throw new Error(`Order with ID ${id} still does not exist after creating sample data`);
          }
        } catch (refreshError) {
          console.error('❌ Failed to create sample data:', refreshError);
          throw new Error(`Order with ID ${id} does not exist and could not create sample data`);
        }
      } else {
        throw new Error(`Order with ID ${id} does not exist. Available orders: ${allOrders.map(o => o.id).join(', ')}`);
      }
    }

    // Transform form data to database format
    let clientId = orderData.clientId;
    let vendorId = orderData.vendorId;

    // If clientId/vendorId not provided, try to find them by name
    if (!clientId && orderData.client) {
      try {
        const clients = await sql`SELECT id FROM clients WHERE company_name ILIKE ${orderData.client}`;
        clientId = clients[0]?.id || null;
        console.log('🔍 Found client ID:', clientId, 'for client:', orderData.client);
      } catch (error) {
        console.warn('⚠️ Could not find client ID for:', orderData.client, error.message);
        // Try to get the first available client as fallback
        try {
          const fallbackClients = await sql`SELECT id FROM clients LIMIT 1`;
          clientId = fallbackClients[0]?.id || null;
          console.log('🔄 Using fallback client ID:', clientId);
        } catch (fallbackError) {
          console.warn('⚠️ No clients available');
          clientId = null;
        }
      }
    }

    if (!vendorId && orderData.vendorName) {
      try {
        const vendors = await sql`SELECT id FROM vendors WHERE company_name ILIKE ${orderData.vendorName}`;
        vendorId = vendors[0]?.id || null;
        console.log('🔍 Found vendor ID:', vendorId, 'for vendor:', orderData.vendorName);
      } catch (error) {
        console.warn('⚠️ Could not find vendor ID for:', orderData.vendorName, error.message);
        // Try to get the first available vendor as fallback
        try {
          const fallbackVendors = await sql`SELECT id FROM vendors LIMIT 1`;
          vendorId = fallbackVendors[0]?.id || null;
          console.log('🔄 Using fallback vendor ID:', vendorId);
        } catch (fallbackError) {
          console.warn('⚠️ No vendors available');
          vendorId = null;
        }
      }
    }

    // Map form fields to database fields
    const description = orderData.description || orderData.typeOfWork || orderData.statusComments || '';
    const amount = parseFloat(orderData.amount || orderData.totalInvoiceValue || 0);
    const status = orderData.status || orderData.currentStatus || 'Pending';
    const priority = orderData.priority || 'Medium';
    const files = orderData.files || {};
    const statusHistory = orderData.statusHistory || [];

    console.log('📊 Mapped data:', {
      clientId,
      vendorId,
      description,
      amount,
      status,
      priority,
      files: Object.keys(files),
      statusHistoryLength: statusHistory.length
    });

    let order;
    try {
      order = await sql`
        UPDATE orders SET
          client_id = ${clientId},
          vendor_id = ${vendorId},
          description = ${description},
          amount = ${amount},
          status = ${status},
          priority = ${priority},
          files = ${JSON.stringify(files)},
          status_history = ${JSON.stringify(statusHistory)},
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ${id}
        RETURNING *
      `;

      if (order.length === 0) {
        console.warn(`⚠️ No rows updated for order ID ${id}, attempting to create new order...`);

        // If update failed, try to create the order with the given ID
        // First, let's try with minimal fields to see what works
        try {
          order = await sql`
            INSERT INTO orders (
              id,
              description,
              amount,
              status
            ) VALUES (
              ${id},
              ${description},
              ${amount},
              'PENDING'
            )
            RETURNING *
          `;
        } catch (insertError) {
          console.error('❌ Insert with minimal fields failed:', insertError.message);

          // Try with just ID and see what happens
          try {
            order = await sql`
              INSERT INTO orders (id) VALUES (${id})
              RETURNING *
            `;
          } catch (finalError) {
            console.error('❌ Even minimal insert failed:', finalError.message);
            throw new Error(`Cannot create order with ID ${id}: ${finalError.message}`);
          }
        }

        console.log('✅ Created new order with ID:', id);
      }
    } catch (sqlError) {
      console.error('❌ SQL Error during update/insert:', sqlError);
      throw new Error(`Database operation failed: ${sqlError.message}`);
    }

    console.log('✅ Order updated successfully:', order[0]);
    return order[0];
  } catch (error) {
    console.error('❌ Error updating order:', error);
    console.error('Error details:', error.message);
    console.error('Stack:', error.stack);
    throw error;
  }
};

// Delete order
export const deleteOrder = async (id) => {
  try {
    const result = await sql`
      DELETE FROM orders
      WHERE id = ${id}
      RETURNING id
    `;

    if (result.length === 0) {
      throw new Error('Order not found');
    }

    return result[0];
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
};

// Update order status
export const updateOrderStatus = async (id, newStatus, comment) => {
  try {
    // Get current order
    const currentOrder = await getOrderById(id);
    if (!currentOrder) {
      throw new Error('Order not found');
    }

    // Update status history
    const statusHistory = currentOrder.status_history || [];
    statusHistory.push({
      status: newStatus,
      date: new Date().toISOString().split('T')[0],
      description: comment || `Status changed to ${newStatus}`
    });

    const order = await sql`
      UPDATE orders SET
        status = ${newStatus},
        status_history = ${JSON.stringify(statusHistory)},
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING *
    `;
    return order[0];
  } catch (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
};



// Get order statistics
export const getOrderStats = async () => {
  try {
    const stats = await sql`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as in_progress_orders,
        COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_orders,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount,
        COUNT(CASE WHEN priority = 'High' THEN 1 END) as high_priority_orders,
        COUNT(CASE WHEN priority = 'Medium' THEN 1 END) as medium_priority_orders,
        COUNT(CASE WHEN priority = 'Low' THEN 1 END) as low_priority_orders
      FROM orders
    `;
    return stats[0];
  } catch (error) {
    console.error('Error fetching order stats:', error);
    throw error;
  }
};

// Search orders
export const searchOrders = async (searchTerm) => {
  try {
    const orders = await sql`
      SELECT 
        o.id,
        o.reference_number,
        o.client_id,
        o.vendor_id,
        o.description,
        o.amount,
        o.status,
        o.priority,
        o.files,
        o.status_history,
        o.created_at,
        o.updated_at,
        c.company_name as client_name,
        v.company_name as vendor_name
      FROM orders o
      LEFT JOIN clients c ON o.client_id = c.id
      LEFT JOIN vendors v ON o.vendor_id = v.id
      WHERE 
        o.reference_number ILIKE ${`%${searchTerm}%`} OR
        o.description ILIKE ${`%${searchTerm}%`} OR
        c.company_name ILIKE ${`%${searchTerm}%`} OR
        v.company_name ILIKE ${`%${searchTerm}%`}
      ORDER BY o.created_at DESC
    `;
    return orders;
  } catch (error) {
    console.error('Error searching orders:', error);
    throw error;
  }
};
